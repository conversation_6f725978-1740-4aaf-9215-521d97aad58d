"""
Cấu hình ứng dụng
"""

import os
from pathlib import Path
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    """Cấu hình ứng dụng"""
    
    # Cấu hình server
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Cấu hình database
    DATABASE_URL: str = "sqlite:///./ai_data_analysis.db"
    
    # Cấu hình file upload
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    UPLOAD_DIR: str = "uploads"
    ALLOWED_EXTENSIONS: list = [
        ".xlsx", ".xls", ".csv",  # Excel files
        ".docx", ".doc", ".txt",  # Word/Text files
        ".pdf",                   # PDF files
        ".jpg", ".jpeg", ".png", ".gif", ".bmp",  # Image files
        ".mp3", ".wav", ".m4a", ".flac"  # Audio files
    ]
    
    # <PERSON><PERSON><PERSON> hình AI/Llama
    LLAMA_MODEL_PATH: Optional[str] = None
    LLAMA_MODEL_NAME: str = "llama-2-7b-chat"
    MAX_TOKENS: int = 2048
    TEMPERATURE: float = 0.7
    
    # Cấu hình bảo mật
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Cấu hình logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Tạo instance settings
settings = Settings()

# Tạo thư mục cần thiết
Path(settings.UPLOAD_DIR).mkdir(exist_ok=True)
