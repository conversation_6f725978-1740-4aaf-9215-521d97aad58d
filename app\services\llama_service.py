"""
Service tích hợp AI Llama cho chatbot
"""

import os
import logging
from typing import Dict, Any, Optional, List
from app.core.config import settings

logger = logging.getLogger(__name__)

class LlamaService:
    """Service để tương tác với Llama AI model"""
    
    def __init__(self):
        self.model = None
        self.is_initialized = False
        self.fallback_mode = True  # Sử dụng fallback responses khi Llama không khả dụng
        
        # Thử khởi tạo Llama
        self._initialize_llama()
    
    def _initialize_llama(self):
        """Khởi tạo Llama model"""
        try:
            # Thử import llama-cpp-python
            from llama_cpp import Llama
            
            # Kiểm tra xem có model path không
            if settings.LLAMA_MODEL_PATH and os.path.exists(settings.LLAMA_MODEL_PATH):
                self.model = Llama(
                    model_path=settings.LLAMA_MODEL_PATH,
                    n_ctx=2048,  # Context window
                    n_threads=4,  # Number of threads
                    verbose=False
                )
                self.is_initialized = True
                self.fallback_mode = False
                logger.info("✅ Llama model initialized successfully!")
            else:
                logger.warning("⚠️ Llama model path not found. Using fallback mode.")
                
        except ImportError:
            logger.warning("⚠️ llama-cpp-python not installed. Using fallback mode.")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Llama: {str(e)}")
    
    def generate_response(
        self, 
        user_message: str, 
        context: Optional[str] = None,
        file_content: Optional[Dict[str, Any]] = None
    ) -> str:
        """Tạo response từ Llama hoặc fallback"""
        
        if self.fallback_mode or not self.is_initialized:
            return self._generate_fallback_response(user_message, context, file_content)
        
        try:
            # Tạo prompt cho Llama
            prompt = self._create_prompt(user_message, context, file_content)
            
            # Generate response từ Llama
            response = self.model(
                prompt,
                max_tokens=settings.MAX_TOKENS,
                temperature=settings.TEMPERATURE,
                stop=["Human:", "User:", "\n\n"]
            )
            
            # Extract text từ response
            generated_text = response['choices'][0]['text'].strip()
            
            return generated_text if generated_text else self._generate_fallback_response(user_message, context, file_content)
            
        except Exception as e:
            logger.error(f"Error generating Llama response: {str(e)}")
            return self._generate_fallback_response(user_message, context, file_content)
    
    def _create_prompt(
        self, 
        user_message: str, 
        context: Optional[str] = None,
        file_content: Optional[Dict[str, Any]] = None
    ) -> str:
        """Tạo prompt cho Llama"""
        
        prompt_parts = [
            "Bạn là một AI assistant chuyên phân tích dữ liệu. Hãy trả lời câu hỏi của người dùng một cách chính xác và hữu ích.",
            ""
        ]
        
        # Thêm context từ file nếu có
        if file_content:
            prompt_parts.append("Thông tin từ file đã upload:")
            prompt_parts.append(self._format_file_content(file_content))
            prompt_parts.append("")
        
        # Thêm context bổ sung nếu có
        if context:
            prompt_parts.append("Context bổ sung:")
            prompt_parts.append(context)
            prompt_parts.append("")
        
        # Thêm câu hỏi của user
        prompt_parts.append(f"Câu hỏi: {user_message}")
        prompt_parts.append("Trả lời:")
        
        return "\n".join(prompt_parts)
    
    def _format_file_content(self, file_content: Dict[str, Any]) -> str:
        """Format nội dung file để đưa vào prompt"""
        
        content_type = file_content.get('type', 'unknown')
        
        if content_type == 'excel':
            summary = file_content.get('summary', {})
            return f"""
Loại file: Excel
Kích thước: {summary.get('shape', [0, 0])[0]} hàng, {summary.get('shape', [0, 0])[1]} cột
Các cột: {', '.join(summary.get('columns', [])[:10])}
Dữ liệu mẫu: {str(summary.get('head', {}))[:500]}
"""
        
        elif content_type in ['text', 'pdf']:
            text = file_content.get('text', '')
            word_count = file_content.get('word_count', 0)
            return f"""
Loại file: {content_type.upper()}
Số từ: {word_count}
Nội dung: {text[:1000]}{'...' if len(text) > 1000 else ''}
"""
        
        elif content_type == 'image':
            text = file_content.get('text', '')
            size = file_content.get('image_size', [0, 0])
            return f"""
Loại file: Ảnh
Kích thước: {size[0]}x{size[1]}
Text trích xuất: {text[:500]}{'...' if len(text) > 500 else ''}
"""
        
        elif content_type == 'audio':
            text = file_content.get('text', '')
            duration = file_content.get('duration', 0)
            return f"""
Loại file: Audio
Thời lượng: {duration:.1f} giây
Text chuyển đổi: {text[:500]}{'...' if len(text) > 500 else ''}
"""
        
        return f"Loại file: {content_type}\nNội dung: {str(file_content)[:500]}"
    
    def _generate_fallback_response(
        self, 
        user_message: str, 
        context: Optional[str] = None,
        file_content: Optional[Dict[str, Any]] = None
    ) -> str:
        """Tạo response fallback khi Llama không khả dụng"""
        
        message_lower = user_message.lower()
        
        # Phân tích file content nếu có
        if file_content:
            return self._analyze_file_content_fallback(user_message, file_content)
        
        # Responses cho các câu hỏi thường gặp
        if any(word in message_lower for word in ['xin chào', 'hello', 'hi', 'chào']):
            return "Xin chào! Tôi là AI assistant phân tích dữ liệu. Hãy upload file và đặt câu hỏi về dữ liệu của bạn."
        
        elif any(word in message_lower for word in ['cảm ơn', 'thank', 'thanks']):
            return "Rất vui được giúp đỡ bạn! Nếu có thêm câu hỏi về dữ liệu, hãy tiếp tục hỏi nhé."
        
        elif any(word in message_lower for word in ['giúp', 'help', 'hướng dẫn']):
            return """Tôi có thể giúp bạn:
1. Phân tích file Excel, CSV - thống kê, tóm tắt dữ liệu
2. Đọc nội dung file Word, PDF, Text
3. Trích xuất text từ ảnh (OCR)
4. Chuyển đổi speech-to-text từ file audio
5. Trả lời câu hỏi về dữ liệu đã upload

Hãy upload file và đặt câu hỏi cụ thể!"""
        
        elif any(word in message_lower for word in ['phân tích', 'analyze', 'analysis']):
            return "Để phân tích dữ liệu, vui lòng upload file trước. Tôi hỗ trợ Excel, Word, PDF, ảnh và audio files."
        
        else:
            return f"""Tôi đã nhận được câu hỏi: "{user_message}"

Hiện tại tôi đang chạy ở chế độ cơ bản. Để có trải nghiệm AI tốt hơn, vui lòng:
1. Cài đặt Llama model
2. Cấu hình LLAMA_MODEL_PATH trong file .env

Tuy nhiên, tôi vẫn có thể giúp phân tích file đã upload. Hãy chọn file và đặt câu hỏi cụ thể!"""
    
    def _analyze_file_content_fallback(self, user_message: str, file_content: Dict[str, Any]) -> str:
        """Phân tích file content với fallback logic"""
        
        content_type = file_content.get('type', 'unknown')
        message_lower = user_message.lower()
        
        if content_type == 'excel':
            return self._analyze_excel_fallback(message_lower, file_content)
        elif content_type in ['text', 'pdf']:
            return self._analyze_text_fallback(message_lower, file_content)
        elif content_type == 'image':
            return self._analyze_image_fallback(message_lower, file_content)
        elif content_type == 'audio':
            return self._analyze_audio_fallback(message_lower, file_content)
        else:
            return f"File loại {content_type} đã được xử lý. Bạn muốn biết thông tin gì cụ thể?"
    
    def _analyze_excel_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích Excel file với fallback logic"""
        summary = file_content.get('summary', {})
        shape = summary.get('shape', [0, 0])
        columns = summary.get('columns', [])
        
        if any(word in message_lower for word in ['tóm tắt', 'summary', 'overview']):
            return f"""📊 Tóm tắt file Excel:
- Kích thước: {shape[0]} hàng, {shape[1]} cột
- Các cột: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}
- Dữ liệu số: {len([c for c in summary.get('dtypes', {}).values() if 'int' in str(c) or 'float' in str(c)])} cột
- Dữ liệu thiếu: {sum(summary.get('null_counts', {}).values())} ô trống"""
        
        elif any(word in message_lower for word in ['cột', 'column', 'field']):
            return f"📋 Các cột trong file:\n" + "\n".join([f"- {col}" for col in columns])
        
        elif any(word in message_lower for word in ['số liệu', 'thống kê', 'statistic']):
            describe = summary.get('describe', {})
            if describe:
                return "📈 Thống kê cơ bản:\n" + "\n".join([f"- {col}: {stats}" for col, stats in list(describe.items())[:3]])
            else:
                return "Không có dữ liệu số để thống kê."
        
        else:
            return f"📊 File Excel có {shape[0]} hàng và {shape[1]} cột. Bạn muốn xem thống kê, danh sách cột, hay thông tin gì khác?"
    
    def _analyze_text_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích text file với fallback logic"""
        text = file_content.get('text', '')
        word_count = file_content.get('word_count', 0)
        
        if any(word in message_lower for word in ['tóm tắt', 'summary']):
            return f"""📄 Tóm tắt văn bản:
- Số từ: {word_count}
- Số ký tự: {len(text)}
- Đoạn đầu: {text[:200]}{'...' if len(text) > 200 else ''}"""
        
        elif any(word in message_lower for word in ['nội dung', 'content', 'text']):
            return f"📄 Nội dung file:\n{text[:1000]}{'...' if len(text) > 1000 else ''}"
        
        elif any(word in message_lower for word in ['từ khóa', 'keyword']):
            words = text.lower().split()
            word_freq = {}
            for word in words:
                if len(word) > 3:
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
            return "🔍 Từ khóa phổ biến:\n" + "\n".join([f"- {word}: {count} lần" for word, count in top_words])
        
        else:
            return f"📄 Văn bản có {word_count} từ. Bạn muốn xem nội dung, tóm tắt, hay tìm từ khóa?"
    
    def _analyze_image_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích image file với fallback logic"""
        text = file_content.get('text', '')
        size = file_content.get('image_size', [0, 0])
        
        if any(word in message_lower for word in ['text', 'chữ', 'nội dung']):
            if text.strip():
                return f"📷 Text trích xuất từ ảnh:\n{text}"
            else:
                return "📷 Không phát hiện text trong ảnh này."
        
        else:
            return f"""📷 Thông tin ảnh:
- Kích thước: {size[0]}x{size[1]} pixels
- Text phát hiện: {'Có' if text.strip() else 'Không'}
{f'- Nội dung: {text[:100]}...' if text.strip() else ''}"""
    
    def _analyze_audio_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích audio file với fallback logic"""
        text = file_content.get('text', '')
        duration = file_content.get('duration', 0)
        
        if any(word in message_lower for word in ['transcript', 'chuyển đổi', 'nội dung']):
            if text.strip():
                return f"🎵 Nội dung chuyển đổi từ audio:\n{text}"
            else:
                return "🎵 Không thể chuyển đổi audio thành text."
        
        else:
            return f"""🎵 Thông tin audio:
- Thời lượng: {duration:.1f} giây
- Chuyển đổi text: {'Thành công' if text.strip() else 'Thất bại'}
{f'- Nội dung: {text[:100]}...' if text.strip() else ''}"""
