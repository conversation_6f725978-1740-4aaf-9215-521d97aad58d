"""
Service tích hợp AI Llama cho chatbot
"""

import os
import logging
from typing import Dict, Any, Optional, List
from app.core.config import settings

logger = logging.getLogger(__name__)

class LlamaService:
    """Service để tương tác với Llama AI model"""
    
    def __init__(self):
        self.model = None
        self.is_initialized = False
        self.fallback_mode = True  # Sử dụng fallback responses khi Llama không khả dụng
        
        # Thử khởi tạo Llama
        self._initialize_llama()
    
    def _initialize_llama(self):
        """Khởi tạo Llama model"""
        try:
            # Thử import llama-cpp-python
            from llama_cpp import Llama
            
            # Kiểm tra xem có model path không
            if settings.LLAMA_MODEL_PATH and os.path.exists(settings.LLAMA_MODEL_PATH):
                self.model = Llama(
                    model_path=settings.LLAMA_MODEL_PATH,
                    n_ctx=2048,  # Context window
                    n_threads=4,  # Number of threads
                    verbose=False
                )
                self.is_initialized = True
                self.fallback_mode = False
                logger.info("✅ Llama model initialized successfully!")
            else:
                logger.warning("⚠️ Llama model path not found. Using fallback mode.")
                
        except ImportError:
            logger.warning("⚠️ llama-cpp-python not installed. Using fallback mode.")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Llama: {str(e)}")
    
    def generate_response(
        self, 
        user_message: str, 
        context: Optional[str] = None,
        file_content: Optional[Dict[str, Any]] = None
    ) -> str:
        """Tạo response từ Llama hoặc fallback"""
        
        if self.fallback_mode or not self.is_initialized:
            return self._generate_fallback_response(user_message, context, file_content)
        
        try:
            # Tạo prompt cho Llama
            prompt = self._create_prompt(user_message, context, file_content)
            
            # Generate response từ Llama
            response = self.model(
                prompt,
                max_tokens=settings.MAX_TOKENS,
                temperature=settings.TEMPERATURE,
                stop=["Human:", "User:", "\n\n"]
            )
            
            # Extract text từ response
            generated_text = response['choices'][0]['text'].strip()
            
            return generated_text if generated_text else self._generate_fallback_response(user_message, context, file_content)
            
        except Exception as e:
            logger.error(f"Error generating Llama response: {str(e)}")
            return self._generate_fallback_response(user_message, context, file_content)
    
    def _create_prompt(
        self, 
        user_message: str, 
        context: Optional[str] = None,
        file_content: Optional[Dict[str, Any]] = None
    ) -> str:
        """Tạo prompt cho Llama"""
        
        prompt_parts = [
            "Bạn là một AI assistant chuyên phân tích dữ liệu. Hãy trả lời câu hỏi của người dùng một cách chính xác và hữu ích.",
            ""
        ]
        
        # Thêm context từ file nếu có
        if file_content:
            prompt_parts.append("Thông tin từ file đã upload:")
            prompt_parts.append(self._format_file_content(file_content))
            prompt_parts.append("")
        
        # Thêm context bổ sung nếu có
        if context:
            prompt_parts.append("Context bổ sung:")
            prompt_parts.append(context)
            prompt_parts.append("")
        
        # Thêm câu hỏi của user
        prompt_parts.append(f"Câu hỏi: {user_message}")
        prompt_parts.append("Trả lời:")
        
        return "\n".join(prompt_parts)
    
    def _format_file_content(self, file_content: Dict[str, Any]) -> str:
        """Format nội dung file để đưa vào prompt"""
        
        content_type = file_content.get('type', 'unknown')
        
        if content_type == 'excel':
            summary = file_content.get('summary', {})
            return f"""
Loại file: Excel
Kích thước: {summary.get('shape', [0, 0])[0]} hàng, {summary.get('shape', [0, 0])[1]} cột
Các cột: {', '.join(summary.get('columns', [])[:10])}
Dữ liệu mẫu: {str(summary.get('head', {}))[:500]}
"""
        
        elif content_type in ['text', 'pdf']:
            text = file_content.get('text', '')
            word_count = file_content.get('word_count', 0)
            return f"""
Loại file: {content_type.upper()}
Số từ: {word_count}
Nội dung: {text[:1000]}{'...' if len(text) > 1000 else ''}
"""
        
        elif content_type == 'image':
            text = file_content.get('text', '')
            size = file_content.get('image_size', [0, 0])
            return f"""
Loại file: Ảnh
Kích thước: {size[0]}x{size[1]}
Text trích xuất: {text[:500]}{'...' if len(text) > 500 else ''}
"""
        
        elif content_type == 'audio':
            text = file_content.get('text', '')
            duration = file_content.get('duration', 0)
            return f"""
Loại file: Audio
Thời lượng: {duration:.1f} giây
Text chuyển đổi: {text[:500]}{'...' if len(text) > 500 else ''}
"""
        
        return f"Loại file: {content_type}\nNội dung: {str(file_content)[:500]}"
    
    def _generate_fallback_response(
        self,
        user_message: str,
        context: Optional[str] = None,
        file_content: Optional[Dict[str, Any]] = None
    ) -> str:
        """Tạo response fallback thông minh khi Llama không khả dụng"""

        message_lower = user_message.lower()

        # Phân tích file content nếu có
        if file_content:
            return self._analyze_file_content_fallback(user_message, file_content)

        # Responses thông minh cho các câu hỏi thường gặp
        if any(word in message_lower for word in ['xin chào', 'hello', 'hi', 'chào', 'hey']):
            return """👋 **Xin chào! Tôi là AI Data Analyst**

🎯 **Tôi có thể giúp bạn:**
- 📊 Phân tích file Excel/CSV (thống kê, insights, trends)
- 📄 Đọc và tóm tắt Word/PDF
- 🖼️ Trích xuất text từ ảnh (OCR)
- 🎵 Chuyển đổi audio thành text
- 💬 Trả lời câu hỏi về dữ liệu

**🚀 Bắt đầu:** Upload file và hỏi tôi bất cứ điều gì!"""

        elif any(word in message_lower for word in ['cảm ơn', 'thank', 'thanks', 'cám ơn']):
            return "🙏 **Rất vui được giúp đỡ bạn!**\n\nNếu có thêm câu hỏi về dữ liệu hoặc cần phân tích gì khác, cứ thoải mái hỏi nhé! 😊"

        elif any(word in message_lower for word in ['giúp', 'help', 'hướng dẫn', 'làm gì', 'có thể']):
            return """🤖 **AI Data Analysis Assistant**

**📊 Phân tích Excel/CSV:**
- Thống kê mô tả (trung bình, min/max, phân phối)
- Phân tích lương, doanh thu, KPI
- Tìm insights và patterns
- Kiểm tra dữ liệu thiếu

**📄 Xử lý văn bản:**
- Tóm tắt nội dung Word/PDF
- Phân tích từ khóa và sentiment
- Trích xuất thông tin quan trọng

**🖼️ Xử lý ảnh:**
- OCR đọc text từ ảnh
- Phân tích nội dung hình ảnh

**🎵 Xử lý audio:**
- Chuyển đổi speech-to-text
- Phân tích nội dung âm thanh

**💡 Ví dụ câu hỏi:**
- "Phân tích lương nhân viên"
- "Tóm tắt báo cáo này"
- "Có bao nhiêu người trong data?"
- "Xu hướng doanh thu như thế nào?"

**🚀 Hãy upload file và bắt đầu!**"""

        elif any(word in message_lower for word in ['phân tích', 'analyze', 'analysis', 'data']):
            return """📊 **Sẵn sàng phân tích dữ liệu!**

**🎯 Để bắt đầu:**
1. Upload file (Excel, CSV, Word, PDF, ảnh, audio)
2. Chọn file từ dropdown
3. Đặt câu hỏi cụ thể

**💡 Câu hỏi mẫu:**
- "Tóm tắt dữ liệu này"
- "Phân tích lương/doanh thu"
- "Có bao nhiêu bản ghi?"
- "Thống kê chi tiết"
- "Tìm insights"

**📈 Tôi sẽ cung cấp:**
- Thống kê chi tiết
- Insights thông minh
- Visualizations mô tả
- Recommendations

Hãy upload file để bắt đầu! 🚀"""

        elif any(word in message_lower for word in ['làm sao', 'how', 'cách nào', 'thế nào']):
            return """🔧 **Hướng dẫn sử dụng:**

**📤 Bước 1: Upload file**
- Kéo thả file vào khung upload
- Hoặc click "Choose File"
- Hỗ trợ: Excel, CSV, Word, PDF, ảnh, audio

**📋 Bước 2: Chọn file**
- Sau khi upload thành công
- Chọn file từ dropdown trong chat
- File sẽ được xử lý tự động

**💬 Bước 3: Đặt câu hỏi**
- Hỏi bất cứ điều gì về dữ liệu
- Tôi sẽ phân tích và trả lời chi tiết
- Có thể hỏi nhiều câu về cùng 1 file

**✨ Mẹo:** Hỏi cụ thể để có kết quả tốt nhất!"""

        elif any(word in message_lower for word in ['tính năng', 'feature', 'có thể làm gì']):
            return """⚡ **Tính năng AI Data Analysis:**

**📊 Excel/CSV Analysis:**
- ✅ Thống kê mô tả đầy đủ
- ✅ Phân tích lương, KPI, metrics
- ✅ Detect patterns và outliers
- ✅ Data quality assessment

**📄 Document Processing:**
- ✅ Tóm tắt thông minh
- ✅ Keyword extraction
- ✅ Sentiment analysis
- ✅ Content categorization

**🖼️ Image Analysis:**
- ✅ OCR text extraction
- ✅ Image content analysis
- ✅ Multi-language support

**🎵 Audio Processing:**
- ✅ Speech-to-text
- ✅ Content summarization
- ✅ Speaker analysis

**🧠 Smart Features:**
- ✅ Context-aware responses
- ✅ Multi-file comparison
- ✅ Trend analysis
- ✅ Predictive insights

**🚀 Powered by advanced AI algorithms!**"""

        else:
            # Phân tích câu hỏi để đưa ra gợi ý thông minh
            suggestions = []

            if any(word in message_lower for word in ['file', 'dữ liệu', 'data']):
                suggestions.append("📤 Upload file để tôi có thể phân tích")

            if any(word in message_lower for word in ['số', 'bao nhiêu', 'count']):
                suggestions.append("📊 Tôi có thể đếm và thống kê dữ liệu")

            if any(word in message_lower for word in ['cao', 'thấp', 'lớn', 'nhỏ', 'max', 'min']):
                suggestions.append("📈 Tôi có thể tìm giá trị cao/thấp nhất")

            if any(word in message_lower for word in ['trung bình', 'average', 'mean']):
                suggestions.append("🧮 Tôi có thể tính trung bình và các chỉ số")

            return f"""🤔 **Tôi hiểu bạn hỏi:** "{user_message}"

💡 **Gợi ý cho bạn:**
{chr(10).join(['- ' + s for s in suggestions]) if suggestions else '- Upload file và đặt câu hỏi cụ thể về dữ liệu'}

**🎯 Để có câu trả lời chính xác:**
1. Upload file dữ liệu
2. Chọn file từ dropdown
3. Hỏi lại câu hỏi với context cụ thể

**💬 Ví dụ tốt:**
- "Phân tích lương trong file Excel này"
- "Tóm tắt nội dung PDF này"
- "Có bao nhiêu nhân viên trong data?"

Tôi sẵn sàng giúp bạn phân tích! 🚀"""
    
    def _analyze_file_content_fallback(self, user_message: str, file_content: Dict[str, Any]) -> str:
        """Phân tích file content với fallback logic"""
        
        content_type = file_content.get('type', 'unknown')
        message_lower = user_message.lower()
        
        if content_type == 'excel':
            return self._analyze_excel_fallback(message_lower, file_content)
        elif content_type in ['text', 'pdf']:
            return self._analyze_text_fallback(message_lower, file_content)
        elif content_type == 'image':
            return self._analyze_image_fallback(message_lower, file_content)
        elif content_type == 'audio':
            return self._analyze_audio_fallback(message_lower, file_content)
        else:
            return f"File loại {content_type} đã được xử lý. Bạn muốn biết thông tin gì cụ thể?"
    
    def _analyze_excel_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích Excel file với fallback logic nâng cao"""
        summary = file_content.get('summary', {})
        shape = summary.get('shape', [0, 0])
        columns = summary.get('columns', [])
        dtypes = summary.get('dtypes', {})
        describe = summary.get('describe', {})
        head_data = summary.get('head', {})
        null_counts = summary.get('null_counts', {})

        # Phân tích thông minh dựa trên tên cột và dữ liệu
        salary_cols = [col for col in columns if any(word in col.lower() for word in ['luong', 'salary', 'wage', 'pay', 'income'])]
        name_cols = [col for col in columns if any(word in col.lower() for word in ['ten', 'name', 'ho', 'firstname', 'lastname'])]
        dept_cols = [col for col in columns if any(word in col.lower() for word in ['phong', 'ban', 'dept', 'department', 'division'])]
        date_cols = [col for col in columns if any(word in col.lower() for word in ['ngay', 'date', 'time', 'thang', 'nam'])]

        if any(word in message_lower for word in ['tóm tắt', 'summary', 'overview', 'nội dung']):
            analysis = f"""📊 **Phân tích chi tiết file Excel:**

**📈 Thông tin cơ bản:**
- Tổng số bản ghi: {shape[0]:,} hàng
- Số trường dữ liệu: {shape[1]} cột
- Dữ liệu thiếu: {sum(null_counts.values())} ô trống

**📋 Cấu trúc dữ liệu:**"""

            if salary_cols:
                analysis += f"\n- 💰 Cột lương/thu nhập: {', '.join(salary_cols)}"
            if name_cols:
                analysis += f"\n- 👤 Cột tên: {', '.join(name_cols)}"
            if dept_cols:
                analysis += f"\n- 🏢 Cột phòng ban: {', '.join(dept_cols)}"
            if date_cols:
                analysis += f"\n- 📅 Cột ngày tháng: {', '.join(date_cols)}"

            # Thống kê lương nếu có
            if salary_cols and describe:
                for col in salary_cols:
                    if col in describe:
                        stats = describe[col]
                        if isinstance(stats, dict):
                            analysis += f"\n\n**💰 Thống kê {col}:**"
                            if 'mean' in stats:
                                analysis += f"\n- Trung bình: {stats['mean']:,.0f}"
                            if 'min' in stats and 'max' in stats:
                                analysis += f"\n- Khoảng: {stats['min']:,.0f} - {stats['max']:,.0f}"
                            if 'count' in stats:
                                analysis += f"\n- Số người: {stats['count']:,.0f}"

            return analysis

        elif any(word in message_lower for word in ['lương', 'salary', 'thu nhập', 'tiền']):
            if salary_cols and describe:
                result = "💰 **Phân tích lương:**\n"
                for col in salary_cols:
                    if col in describe:
                        stats = describe[col]
                        if isinstance(stats, dict):
                            result += f"\n**{col}:**"
                            if 'count' in stats:
                                result += f"\n- Số nhân viên: {stats['count']:,.0f}"
                            if 'mean' in stats:
                                result += f"\n- Lương trung bình: {stats['mean']:,.0f} VNĐ"
                            if 'std' in stats:
                                result += f"\n- Độ lệch chuẩn: {stats['std']:,.0f}"
                            if 'min' in stats and 'max' in stats:
                                result += f"\n- Khoảng lương: {stats['min']:,.0f} - {stats['max']:,.0f} VNĐ"
                            if '25%' in stats and '75%' in stats:
                                result += f"\n- Q1-Q3: {stats['25%']:,.0f} - {stats['75%']:,.0f} VNĐ"
                return result
            else:
                return "Không tìm thấy cột lương trong dữ liệu. Các cột hiện có: " + ", ".join(columns)

        elif any(word in message_lower for word in ['nhân viên', 'người', 'employee', 'staff']):
            result = f"👥 **Thông tin nhân viên:**\n- Tổng số: {shape[0]:,} người"
            if name_cols:
                result += f"\n- Cột tên: {', '.join(name_cols)}"
            if dept_cols:
                result += f"\n- Có thông tin phòng ban: {', '.join(dept_cols)}"
            return result

        elif any(word in message_lower for word in ['cột', 'column', 'field', 'trường']):
            result = f"📋 **Danh sách {len(columns)} cột:**\n"
            for i, col in enumerate(columns, 1):
                dtype = dtypes.get(col, 'unknown')
                null_count = null_counts.get(col, 0)
                result += f"{i}. **{col}** ({dtype})"
                if null_count > 0:
                    result += f" - {null_count} giá trị thiếu"
                result += "\n"
            return result

        elif any(word in message_lower for word in ['thống kê', 'statistic', 'số liệu']):
            if describe:
                result = "📈 **Thống kê chi tiết:**\n"
                for col, stats in list(describe.items())[:3]:
                    if isinstance(stats, dict):
                        result += f"\n**{col}:**"
                        for key, value in stats.items():
                            if isinstance(value, (int, float)):
                                result += f"\n- {key}: {value:,.2f}"
                        result += "\n"
                return result
            else:
                return "Không có dữ liệu số để thống kê."

        else:
            # Phân tích tự động dựa trên nội dung
            insights = []
            if salary_cols:
                insights.append(f"💰 Phát hiện dữ liệu lương: {', '.join(salary_cols)}")
            if shape[0] > 1000:
                insights.append(f"📊 Dataset lớn với {shape[0]:,} bản ghi")
            if sum(null_counts.values()) > 0:
                insights.append(f"⚠️ Có {sum(null_counts.values())} ô dữ liệu thiếu")

            result = f"📊 **File Excel phân tích:**\n- {shape[0]:,} hàng × {shape[1]} cột"
            if insights:
                result += "\n\n**🔍 Phát hiện:**\n- " + "\n- ".join(insights)
            result += "\n\n**💡 Bạn có thể hỏi:**\n- 'Phân tích lương'\n- 'Thống kê nhân viên'\n- 'Tóm tắt dữ liệu'"
            return result
    
    def _analyze_text_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích text file với fallback logic nâng cao"""
        text = file_content.get('text', '')
        word_count = file_content.get('word_count', 0)
        char_count = len(text)

        # Phân tích nâng cao
        sentences = text.split('.')
        paragraphs = text.split('\n\n')

        if any(word in message_lower for word in ['tóm tắt', 'summary', 'overview']):
            # Tạo tóm tắt thông minh
            key_sentences = []
            for sentence in sentences[:5]:  # Lấy 5 câu đầu
                if len(sentence.strip()) > 20:
                    key_sentences.append(sentence.strip())

            summary_text = '. '.join(key_sentences[:3])
            if len(summary_text) > 300:
                summary_text = summary_text[:300] + "..."

            return f"""📄 **Phân tích văn bản:**

**📊 Thống kê:**
- Số từ: {word_count:,}
- Số ký tự: {char_count:,}
- Số câu: {len([s for s in sentences if len(s.strip()) > 5]):,}
- Số đoạn: {len([p for p in paragraphs if len(p.strip()) > 10]):,}

**📝 Tóm tắt nội dung:**
{summary_text}

**🔍 Phân tích nhanh:**
- Độ dài: {'Dài' if word_count > 1000 else 'Trung bình' if word_count > 300 else 'Ngắn'}
- Kiểu văn bản: {'Chính thức' if any(word in text.lower() for word in ['công ty', 'báo cáo', 'thông báo']) else 'Thông thường'}"""

        elif any(word in message_lower for word in ['nội dung', 'content', 'text', 'đọc']):
            # Hiển thị nội dung có format
            preview = text[:1500] if len(text) > 1500 else text
            return f"""📄 **Nội dung file:**

{preview}{'...\n\n*[Đã cắt bớt để hiển thị]*' if len(text) > 1500 else ''}

**📊 Thông tin:** {word_count:,} từ, {char_count:,} ký tự"""

        elif any(word in message_lower for word in ['từ khóa', 'keyword', 'phân tích từ']):
            # Phân tích từ khóa thông minh hơn
            import re
            words = re.findall(r'\b\w+\b', text.lower())

            # Loại bỏ stop words tiếng Việt
            stop_words = {'và', 'của', 'trong', 'với', 'là', 'có', 'được', 'này', 'đó', 'cho', 'từ', 'một', 'các', 'để', 'không', 'về', 'theo', 'như', 'khi', 'đã', 'sẽ', 'bị', 'do', 'nếu', 'mà', 'tại', 'trên', 'dưới'}

            word_freq = {}
            for word in words:
                if len(word) > 3 and word not in stop_words:
                    word_freq[word] = word_freq.get(word, 0) + 1

            top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:15]

            result = "🔍 **Từ khóa quan trọng:**\n"
            for i, (word, count) in enumerate(top_words, 1):
                percentage = (count / len(words)) * 100
                result += f"{i}. **{word}**: {count} lần ({percentage:.1f}%)\n"

            return result

        elif any(word in message_lower for word in ['cảm xúc', 'sentiment', 'tone']):
            # Phân tích cảm xúc đơn giản
            positive_words = ['tốt', 'hay', 'xuất sắc', 'tuyệt vời', 'thành công', 'hiệu quả', 'tích cực']
            negative_words = ['xấu', 'tệ', 'thất bại', 'khó khăn', 'vấn đề', 'lỗi', 'tiêu cực']

            pos_count = sum(1 for word in positive_words if word in text.lower())
            neg_count = sum(1 for word in negative_words if word in text.lower())

            if pos_count > neg_count:
                sentiment = "😊 Tích cực"
            elif neg_count > pos_count:
                sentiment = "😔 Tiêu cực"
            else:
                sentiment = "😐 Trung tính"

            return f"""🎭 **Phân tích cảm xúc:**
- Tông điệu: {sentiment}
- Từ tích cực: {pos_count}
- Từ tiêu cực: {neg_count}
- Đánh giá: {'Văn bản mang tính tích cực' if pos_count > neg_count else 'Văn bản mang tính tiêu cực' if neg_count > pos_count else 'Văn bản khá trung tính'}"""

        else:
            # Phân tích tự động
            insights = []
            if word_count > 2000:
                insights.append("📚 Văn bản dài, phù hợp để phân tích chi tiết")
            if any(word in text.lower() for word in ['báo cáo', 'thống kê', 'số liệu']):
                insights.append("📊 Có vẻ là báo cáo hoặc tài liệu phân tích")
            if any(word in text.lower() for word in ['email', '@', 'gửi', 'nhận']):
                insights.append("📧 Có thể là email hoặc thư từ")

            result = f"""📄 **Phân tích tự động:**
- Loại: {'PDF' if file_content.get('type') == 'pdf' else 'Văn bản'}
- Kích thước: {word_count:,} từ, {char_count:,} ký tự
- Độ phức tạp: {'Cao' if word_count > 1000 else 'Trung bình'}"""

            if insights:
                result += "\n\n**🔍 Nhận xét:**\n- " + "\n- ".join(insights)

            result += "\n\n**💡 Bạn có thể hỏi:**\n- 'Tóm tắt nội dung'\n- 'Phân tích từ khóa'\n- 'Đọc nội dung'\n- 'Phân tích cảm xúc'"

            return result
    
    def _analyze_image_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích image file với fallback logic"""
        text = file_content.get('text', '')
        size = file_content.get('image_size', [0, 0])
        
        if any(word in message_lower for word in ['text', 'chữ', 'nội dung']):
            if text.strip():
                return f"📷 Text trích xuất từ ảnh:\n{text}"
            else:
                return "📷 Không phát hiện text trong ảnh này."
        
        else:
            return f"""📷 Thông tin ảnh:
- Kích thước: {size[0]}x{size[1]} pixels
- Text phát hiện: {'Có' if text.strip() else 'Không'}
{f'- Nội dung: {text[:100]}...' if text.strip() else ''}"""
    
    def _analyze_audio_fallback(self, message_lower: str, file_content: Dict[str, Any]) -> str:
        """Phân tích audio file với fallback logic"""
        text = file_content.get('text', '')
        duration = file_content.get('duration', 0)
        
        if any(word in message_lower for word in ['transcript', 'chuyển đổi', 'nội dung']):
            if text.strip():
                return f"🎵 Nội dung chuyển đổi từ audio:\n{text}"
            else:
                return "🎵 Không thể chuyển đổi audio thành text."
        
        else:
            return f"""🎵 Thông tin audio:
- Thời lượng: {duration:.1f} giây
- Chuyển đổi text: {'Thành công' if text.strip() else 'Thất bại'}
{f'- Nội dung: {text[:100]}...' if text.strip() else ''}"""
