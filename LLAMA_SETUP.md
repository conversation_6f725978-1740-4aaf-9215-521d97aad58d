# Hướng dẫn cài đặt Llama Model (T<PERSON><PERSON> chọn)

## ⚠️ Lưu ý quan trọng
Ứng dụng hiện tại **đã hoạt động tốt** với AI fallback mode. Việ<PERSON> cài Llama chỉ để có AI responses tốt hơn.

## 🎯 Tùy chọn 1: <PERSON><PERSON> dụ<PERSON> (Khuyến nghị - <PERSON><PERSON> nhất)

### Cài đặt Ollama:
1. **T<PERSON>i <PERSON>**: https://ollama.ai/download
2. **Cài đặt và chạy**
3. **Tải model**:
```bash
ollama pull llama2:7b
# hoặc model nhỏ hơn
ollama pull llama2:3b
```

### Cập nhật code để dùng Ollama:
```python
# Thêm vào app/services/llama_service.py
import requests

def call_ollama(prompt):
    response = requests.post('http://localhost:11434/api/generate', 
        json={
            'model': 'llama2:7b',
            'prompt': prompt,
            'stream': False
        })
    return response.json()['response']
```

## 🎯 Tùy chọn 2: Llama-cpp-python (Nâng cao)

### Cài đặt:
```bash
pip install llama-cpp-python
```

### Tải model:
1. **Tải model GGUF** từ HuggingFace:
   - Llama 2 7B: https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF
   - Tải file `.gguf` (khoảng 4-8GB)

2. **Cấu hình trong .env**:
```env
LLAMA_MODEL_PATH=path/to/your/model.gguf
```

## 🎯 Tùy chọn 3: OpenAI API (Đơn giản nhất)

### Cài đặt:
```bash
pip install openai
```

### Cấu hình:
```env
OPENAI_API_KEY=your_api_key_here
```

### Code integration:
```python
import openai

def call_openai(prompt):
    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content
```

## 🎯 Tùy chọn 4: Giữ nguyên Fallback Mode (Khuyến nghị cho demo)

**Ưu điểm của fallback mode hiện tại:**
- ✅ Không cần tải model lớn (4-8GB)
- ✅ Chạy nhanh, không cần GPU
- ✅ Phân tích file chính xác
- ✅ Responses thông minh cho từng loại file
- ✅ Hoàn toàn miễn phí

## 🧪 Test AI hiện tại

Hãy thử chat với file Excel đã upload:

**Câu hỏi gợi ý:**
- "Tóm tắt file Excel này"
- "Có bao nhiêu nhân viên?"
- "Mức lương trung bình là bao nhiêu?"
- "Phân tích dữ liệu lương theo phòng ban"

## 🚀 Kết luận

**Khuyến nghị**: Hãy test ứng dụng với fallback mode trước. Nếu cần AI responses phức tạp hơn, thì cài Ollama (dễ nhất) hoặc dùng OpenAI API.

Ứng dụng hiện tại đã rất mạnh mẽ cho việc phân tích dữ liệu!
