"""
AI Data Analysis Application với FastAPI
Ứng dụng phân tích dữ liệu đa dạng với AI chatbot
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import uvicorn
import os
from pathlib import Path

# Import các module của ứng dụng
from app.routers import file_upload, chatbot, analysis
from app.core.config import settings
from app.core.database import init_db

# Tạo ứng dụng FastAPI
app = FastAPI(
    title="AI Data Analysis Platform",
    description="Ứng dụng phân tích dữ liệu đa dạng với AI chatbot sử dụng Llama",
    version="1.0.0"
)

# Thiết lập static files và templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Include routers
app.include_router(file_upload.router, prefix="/api/v1", tags=["File Upload"])
app.include_router(chatbot.router, prefix="/api/v1", tags=["Chatbot"])
app.include_router(analysis.router, prefix="/api/v1", tags=["Analysis"])

@app.on_event("startup")
async def startup_event():
    """Khởi tạo ứng dụng khi start"""
    # Tạo thư mục cần thiết
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    os.makedirs("templates", exist_ok=True)
    
    # Khởi tạo database
    await init_db()
    
    print("🚀 AI Data Analysis Platform đã khởi động!")

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Trang chủ của ứng dụng"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "AI Data Analysis Platform is running!"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
