# AI Data Analysis Platform

Ứng dụng phân tích dữ liệu đa dạng với AI chatbot sử dụng FastAPI và Llama.

## Tính năng chính

- 📁 **Upload đa dạng file**: Excel, Word, PDF, ảnh, MP3
- 🤖 **AI Chatbot**: Sử dụng Llama để phân tích và trả lời
- 📊 **Phân tích dữ liệu**: Trích xuất và xử lý nội dung từ các file
- 🌐 **Giao diện web**: Interface thân thiện và responsive
- ⚡ **FastAPI**: Backend hiệu suất cao với async/await

## Cấu trúc dự án

```
ai-data-analysis/
├── app/
│   ├── core/           # Cấu hình và database
│   ├── routers/        # API endpoints
│   ├── services/       # Business logic
│   ├── models/         # Pydantic models
│   └── utils/          # Utilities
├── static/             # CSS, JS, images
├── templates/          # HTML templates
├── uploads/            # File uploads
├── main.py            # Entry point
├── requirements.txt   # Dependencies
└── .env              # Environment variables
```

## Cài đặt

1. **Clone repository và cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

2. **Cấu hình môi trường:**
Chỉnh sửa file `.env` theo nhu cầu của bạn.

3. **Chạy ứng dụng:**
```bash
python main.py
```

4. **Truy cập ứng dụng:**
Mở trình duyệt và truy cập: http://127.0.0.1:8000

## API Endpoints

- `GET /` - Trang chủ
- `POST /api/v1/upload` - Upload file
- `POST /api/v1/chat` - Chat với AI
- `GET /api/v1/analysis/{file_id}` - Phân tích file
- `GET /health` - Health check

## Các loại file được hỗ trợ

- **Excel**: .xlsx, .xls, .csv
- **Word**: .docx, .doc, .txt
- **PDF**: .pdf
- **Ảnh**: .jpg, .jpeg, .png, .gif, .bmp
- **Audio**: .mp3, .wav, .m4a, .flac

## Công nghệ sử dụng

- **Backend**: FastAPI, SQLAlchemy, Pydantic
- **AI**: Llama, Transformers, LangChain
- **File Processing**: pandas, python-docx, PyPDF2, Pillow, pydub
- **Frontend**: HTML, CSS, JavaScript
- **Database**: SQLite (có thể thay đổi)

## Phát triển

Dự án được tổ chức theo kiến trúc modular, dễ dàng mở rộng và bảo trì.

## License

MIT License
