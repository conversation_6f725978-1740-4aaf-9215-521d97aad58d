# 🚀 Hướng dẫn cài đặt Ollama cho AI thông minh hơn

## 📋 Y<PERSON><PERSON> cầu hệ thống
- **RAM**: T<PERSON>i thiểu 8GB (khuyến nghị 16GB+)
- **Dung lượng**: 5-10GB trống
- **OS**: Windows 10/11, macOS, Linux

## 🔧 Bước 1: Cài đặt Ollama

### Windows:
1. <PERSON><PERSON><PERSON> cập: https://ollama.ai/download
2. Tải file **Ollama-windows-amd64.exe**
3. Chạy file .exe và cài đặt
4. Ollama sẽ tự động chạy ở background

### Kiểm tra cài đặt:
```bash
# Mở Command Prompt hoặc PowerShell
ollama --version
```

## 🤖 Bước 2: Tải Llama Model

### Chọn model phù hợp:

```bash
# Model nhỏ, nhanh (1.7GB) - <PERSON> máy yếu
ollama pull llama3.2:1b

# Model trung bình (3.8GB) - <PERSON><PERSON><PERSON><PERSON><PERSON> nghị
ollama pull llama3.2:3b

# Model lớn, th<PERSON><PERSON> minh (7.4GB) - <PERSON> m<PERSON> mạnh
ollama pull llama3.2:7b

# Model rất lớn (70GB) - Chỉ cho máy rất mạnh
ollama pull llama3.2:70b
```

**Khuyến nghị**: Bắt đầu với `llama3.2:3b`

## 🧪 Bước 3: Test Model

```bash
# Test model đã tải
ollama run llama3.2:3b "Xin chào, bạn có thể phân tích dữ liệu không?"

# Thoát khỏi chat mode
/bye
```

## 🔗 Bước 4: Cài requests cho Python

```bash
pip install requests
```

## ✅ Bước 5: Restart ứng dụng

1. **Dừng server** hiện tại (Ctrl+C trong terminal)
2. **Chạy lại**:
```bash
python run.py
```

## 🎯 Kiểm tra kết quả

Khi ứng dụng khởi động, bạn sẽ thấy:
```
✅ Ollama connected! Using model: llama3.2:3b
```

Thay vì:
```
⚠️ llama-cpp-python not installed - using fallback mode
```

## 🚀 Test AI mới

1. **Upload file Excel**
2. **Hỏi**: "Phân tích chi tiết file này"
3. **Kết quả**: AI sẽ đưa ra phân tích thông minh, chi tiết hơn nhiều!

## 🔧 Troubleshooting

### Lỗi "Ollama not found":
```bash
# Kiểm tra Ollama có chạy không
ollama list

# Nếu không chạy, start lại
ollama serve
```

### Lỗi "No models found":
```bash
# Tải model
ollama pull llama3.2:3b

# Kiểm tra models đã tải
ollama list
```

### Lỗi "Connection refused":
- Restart Ollama
- Kiểm tra port 11434 có bị block không

## 📊 So sánh hiệu suất

| Model | Kích thước | RAM cần | Tốc độ | Chất lượng |
|-------|------------|---------|--------|------------|
| 1b    | 1.7GB      | 4GB     | Rất nhanh | Cơ bản |
| 3b    | 3.8GB      | 8GB     | Nhanh   | Tốt |
| 7b    | 7.4GB      | 16GB    | Trung bình | Rất tốt |
| 70b   | 70GB       | 64GB+   | Chậm    | Xuất sắc |

## 🎉 Kết quả mong đợi

Sau khi cài đặt thành công, AI sẽ:

✅ **Hiểu ngữ cảnh** tốt hơn nhiều
✅ **Phân tích sâu** dữ liệu Excel
✅ **Trả lời tự nhiên** như con người
✅ **Đưa ra insights** thông minh
✅ **Xử lý câu hỏi phức tạp**

## 💡 Tips sử dụng

1. **Hỏi cụ thể**: "Phân tích xu hướng lương theo phòng ban"
2. **Yêu cầu so sánh**: "So sánh hiệu suất giữa các nhóm"
3. **Đặt câu hỏi mở**: "Có insights gì thú vị từ dữ liệu này?"

## 🔄 Nâng cấp Model

```bash
# Xóa model cũ
ollama rm llama3.2:3b

# Tải model mới
ollama pull llama3.2:7b
```

Ứng dụng sẽ tự động sử dụng model mới nhất!

---

**🎯 Mục tiêu**: Biến AI từ "template responses" thành "intelligent analyst"!
