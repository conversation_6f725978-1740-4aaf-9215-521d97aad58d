// AI Data Analysis Platform JavaScript

let currentSessionId = null;

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    loadFileList();
    setupEventListeners();
    currentSessionId = 'session_' + Date.now();
});

function setupEventListeners() {
    // File upload form
    document.getElementById('uploadForm').addEventListener('submit', handleFileUpload);
    
    // Chat form
    document.getElementById('chatForm').addEventListener('submit', handleChatSubmit);
    
    // File input change
    document.getElementById('fileInput').addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            validateFile(file);
        }
    });
}

async function handleFileUpload(event) {
    event.preventDefault();
    
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (!file) {
        showAlert('Vui lòng chọn file để upload!', 'warning');
        return;
    }
    
    if (!validateFile(file)) {
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    // Show progress
    document.getElementById('uploadProgress').style.display = 'block';
    document.getElementById('uploadResult').innerHTML = '';
    
    try {
        const response = await fetch('/api/v1/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showUploadSuccess(result);
            fileInput.value = ''; // Clear input
            loadFileList(); // Refresh file list
        } else {
            showAlert(result.detail || 'Upload failed', 'danger');
        }
    } catch (error) {
        showAlert('Lỗi kết nối: ' + error.message, 'danger');
    } finally {
        document.getElementById('uploadProgress').style.display = 'none';
    }
}

function validateFile(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'text/plain',
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/bmp',
        'audio/mpeg',
        'audio/wav',
        'audio/mp4'
    ];
    
    if (file.size > maxSize) {
        showAlert('File quá lớn! Kích thước tối đa là 50MB.', 'warning');
        return false;
    }
    
    // Check file extension as fallback
    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.xlsx', '.xls', '.csv', '.docx', '.doc', '.txt', '.pdf', 
                              '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.mp3', '.wav', '.m4a', '.flac'];
    
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
    
    if (!hasValidExtension) {
        showAlert('Loại file không được hỗ trợ!', 'warning');
        return false;
    }
    
    return true;
}

function showUploadSuccess(result) {
    const html = `
        <div class="alert alert-success">
            <h6><i class="fas fa-check-circle me-2"></i>Upload thành công!</h6>
            <p class="mb-1"><strong>File:</strong> ${result.filename}</p>
            <p class="mb-1"><strong>Loại:</strong> ${result.file_type}</p>
            <p class="mb-1"><strong>Kích thước:</strong> ${formatFileSize(result.file_size)}</p>
            <p class="mb-0"><strong>Trạng thái:</strong> <span class="badge bg-info">${result.status}</span></p>
        </div>
    `;
    document.getElementById('uploadResult').innerHTML = html;
}

async function loadFileList() {
    try {
        const response = await fetch('/api/v1/files');
        const data = await response.json();
        
        displayFileList(data.files);
        updateFileSelect(data.files);
    } catch (error) {
        document.getElementById('fileList').innerHTML = 
            '<p class="text-danger">Lỗi tải danh sách file: ' + error.message + '</p>';
    }
}

function displayFileList(files) {
    const fileListDiv = document.getElementById('fileList');
    
    if (files.length === 0) {
        fileListDiv.innerHTML = '<p class="text-muted">Chưa có file nào được upload.</p>';
        return;
    }
    
    const html = files.map(file => `
        <div class="file-item">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${file.filename}</h6>
                    <small class="text-muted">
                        ${file.file_type} • ${formatFileSize(file.file_size)} • 
                        ${new Date(file.upload_time).toLocaleString('vi-VN')}
                    </small>
                </div>
                <div class="text-end">
                    <span class="file-status status-${file.processed}">${getStatusText(file.processed)}</span>
                    <div class="mt-1">
                        <button class="btn btn-sm btn-outline-primary btn-file-action" 
                                onclick="viewFileAnalysis(${file.id})">
                            <i class="fas fa-eye"></i> Xem
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-file-action" 
                                onclick="deleteFile(${file.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    fileListDiv.innerHTML = html;
}

function updateFileSelect(files) {
    const select = document.getElementById('fileSelect');
    select.innerHTML = '<option value="">Chọn file...</option>';
    
    files.filter(f => f.processed === 'completed').forEach(file => {
        const option = document.createElement('option');
        option.value = file.id;
        option.textContent = file.filename;
        select.appendChild(option);
    });
}

async function handleChatSubmit(event) {
    event.preventDefault();
    
    const messageInput = document.getElementById('messageInput');
    const fileSelect = document.getElementById('fileSelect');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessageToChat(message, 'user');
    
    // Clear input
    messageInput.value = '';
    
    // Show typing indicator
    const typingId = addTypingIndicator();
    
    try {
        const response = await fetch('/api/v1/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                session_id: currentSessionId,
                file_id: fileSelect.value ? parseInt(fileSelect.value) : null
            })
        });
        
        const result = await response.json();
        
        // Remove typing indicator
        removeTypingIndicator(typingId);
        
        if (response.ok) {
            addMessageToChat(result.response, 'ai');
        } else {
            addMessageToChat('Lỗi: ' + (result.detail || 'Không thể xử lý tin nhắn'), 'ai');
        }
    } catch (error) {
        removeTypingIndicator(typingId);
        addMessageToChat('Lỗi kết nối: ' + error.message, 'ai');
    }
}

function addMessageToChat(message, sender) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    messageDiv.innerHTML = `<strong>${sender === 'user' ? 'Bạn' : 'AI'}:</strong> ${message}`;
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addTypingIndicator() {
    const chatMessages = document.getElementById('chatMessages');
    const typingDiv = document.createElement('div');
    const typingId = 'typing_' + Date.now();
    typingDiv.id = typingId;
    typingDiv.className = 'message ai-message';
    typingDiv.innerHTML = '<strong>AI:</strong> <span class="loading"></span> Đang suy nghĩ...';
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    return typingId;
}

function removeTypingIndicator(typingId) {
    const typingDiv = document.getElementById(typingId);
    if (typingDiv) {
        typingDiv.remove();
    }
}

async function viewFileAnalysis(fileId) {
    try {
        const response = await fetch(`/api/v1/analysis/${fileId}`);
        const data = await response.json();
        
        if (response.ok) {
            showAnalysisModal(data);
        } else {
            showAlert(data.detail || 'Không thể tải phân tích file', 'warning');
        }
    } catch (error) {
        showAlert('Lỗi: ' + error.message, 'danger');
    }
}

async function deleteFile(fileId) {
    if (!confirm('Bạn có chắc muốn xóa file này?')) return;
    
    try {
        const response = await fetch(`/api/v1/files/${fileId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showAlert('File đã được xóa thành công!', 'success');
            loadFileList();
        } else {
            const data = await response.json();
            showAlert(data.detail || 'Không thể xóa file', 'danger');
        }
    } catch (error) {
        showAlert('Lỗi: ' + error.message, 'danger');
    }
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getStatusText(status) {
    const statusMap = {
        'pending': 'Chờ xử lý',
        'processing': 'Đang xử lý',
        'completed': 'Hoàn thành',
        'failed': 'Lỗi'
    };
    return statusMap[status] || status;
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.row'));
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
