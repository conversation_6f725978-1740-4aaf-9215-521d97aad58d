"""
Service xử lý các loại file khác nhau
"""

import os
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional
import logging

# Import libraries cho từng loại file
try:
    from docx import Document
except ImportError:
    Document = None

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    from PIL import Image
    import pytesseract
except ImportError:
    Image = None
    pytesseract = None

try:
    from pydub import AudioSegment
    import speech_recognition as sr
except ImportError:
    AudioSegment = None
    sr = None

logger = logging.getLogger(__name__)

class FileProcessor:
    """Class xử lý các loại file khác nhau"""
    
    def __init__(self):
        self.supported_extensions = {
            'excel': ['.xlsx', '.xls', '.csv'],
            'word': ['.docx', '.doc', '.txt'],
            'pdf': ['.pdf'],
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp'],
            'audio': ['.mp3', '.wav', '.m4a', '.flac']
        }
    
    def get_file_type(self, filename: str) -> str:
        """Xác định loại file dựa trên extension"""
        ext = Path(filename).suffix.lower()
        
        for file_type, extensions in self.supported_extensions.items():
            if ext in extensions:
                return file_type
        
        return 'unknown'
    
    def process_file(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """Xử lý file và trích xuất nội dung"""
        try:
            if file_type == 'excel':
                return self._process_excel(file_path)
            elif file_type == 'word':
                return self._process_word(file_path)
            elif file_type == 'pdf':
                return self._process_pdf(file_path)
            elif file_type == 'image':
                return self._process_image(file_path)
            elif file_type == 'audio':
                return self._process_audio(file_path)
            else:
                return {
                    'success': False,
                    'error': f'Unsupported file type: {file_type}',
                    'content': None
                }
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'content': None
            }
    
    def _process_excel(self, file_path: str) -> Dict[str, Any]:
        """Xử lý file Excel/CSV"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            # Tạo summary của data
            summary = {
                'shape': df.shape,
                'columns': df.columns.tolist(),
                'dtypes': df.dtypes.to_dict(),
                'head': df.head().to_dict(),
                'describe': df.describe().to_dict() if df.select_dtypes(include='number').shape[1] > 0 else {},
                'null_counts': df.isnull().sum().to_dict()
            }
            
            return {
                'success': True,
                'content': {
                    'type': 'excel',
                    'summary': summary,
                    'raw_data': df.to_dict(),
                    'text_representation': df.to_string()
                },
                'error': None
            }
        except Exception as e:
            return {'success': False, 'error': str(e), 'content': None}
    
    def _process_word(self, file_path: str) -> Dict[str, Any]:
        """Xử lý file Word/Text"""
        try:
            if file_path.endswith('.txt'):
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
            elif Document and file_path.endswith('.docx'):
                doc = Document(file_path)
                content = '\n'.join([paragraph.text for paragraph in doc.paragraphs])
            else:
                return {'success': False, 'error': 'Word processing not available', 'content': None}
            
            return {
                'success': True,
                'content': {
                    'type': 'text',
                    'text': content,
                    'word_count': len(content.split()),
                    'char_count': len(content)
                },
                'error': None
            }
        except Exception as e:
            return {'success': False, 'error': str(e), 'content': None}
    
    def _process_pdf(self, file_path: str) -> Dict[str, Any]:
        """Xử lý file PDF"""
        try:
            if not PyPDF2:
                return {'success': False, 'error': 'PDF processing not available', 'content': None}
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ''
                for page in pdf_reader.pages:
                    text += page.extract_text()
            
            return {
                'success': True,
                'content': {
                    'type': 'pdf',
                    'text': text,
                    'page_count': len(pdf_reader.pages),
                    'word_count': len(text.split()),
                    'char_count': len(text)
                },
                'error': None
            }
        except Exception as e:
            return {'success': False, 'error': str(e), 'content': None}
    
    def _process_image(self, file_path: str) -> Dict[str, Any]:
        """Xử lý file ảnh (OCR)"""
        try:
            if not Image or not pytesseract:
                return {'success': False, 'error': 'Image processing not available', 'content': None}
            
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)
            
            return {
                'success': True,
                'content': {
                    'type': 'image',
                    'text': text,
                    'image_size': image.size,
                    'image_mode': image.mode,
                    'word_count': len(text.split()),
                    'char_count': len(text)
                },
                'error': None
            }
        except Exception as e:
            return {'success': False, 'error': str(e), 'content': None}
    
    def _process_audio(self, file_path: str) -> Dict[str, Any]:
        """Xử lý file audio (Speech-to-Text)"""
        try:
            if not AudioSegment or not sr:
                return {'success': False, 'error': 'Audio processing not available', 'content': None}
            
            # Convert audio to wav if needed
            audio = AudioSegment.from_file(file_path)
            wav_path = file_path.replace(Path(file_path).suffix, '.wav')
            audio.export(wav_path, format="wav")
            
            # Speech recognition
            recognizer = sr.Recognizer()
            with sr.AudioFile(wav_path) as source:
                audio_data = recognizer.record(source)
                text = recognizer.recognize_google(audio_data, language='vi-VN')
            
            # Clean up temp wav file
            if wav_path != file_path:
                os.remove(wav_path)
            
            return {
                'success': True,
                'content': {
                    'type': 'audio',
                    'text': text,
                    'duration': len(audio) / 1000,  # seconds
                    'word_count': len(text.split()),
                    'char_count': len(text)
                },
                'error': None
            }
        except Exception as e:
            return {'success': False, 'error': str(e), 'content': None}
