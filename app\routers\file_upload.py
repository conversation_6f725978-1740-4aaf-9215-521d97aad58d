"""
Router cho upload và xử lý file
"""

from fastapi import APIRouter, File, UploadFile, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
import os
import uuid
from pathlib import Path
import shutil
from typing import List

from app.core.database import get_db, UploadedFile
from app.core.config import settings
from app.services.file_processor import FileProcessor

router = APIRouter()
file_processor = FileProcessor()

@router.post("/upload")
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload và xử lý file"""
    
    # Kiểm tra loại file
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in [ext for exts in file_processor.supported_extensions.values() for ext in exts]:
        raise HTTPException(
            status_code=400,
            detail=f"File type {file_extension} is not supported"
        )
    
    # <PERSON><PERSON><PERSON> tra kích thước file
    file_size = 0
    content = await file.read()
    file_size = len(content)
    
    if file_size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File size {file_size} exceeds maximum allowed size {settings.MAX_FILE_SIZE}"
        )
    
    # Tạo tên file unique
    file_id = str(uuid.uuid4())
    filename = f"{file_id}{file_extension}"
    file_path = os.path.join(settings.UPLOAD_DIR, filename)
    
    # Lưu file
    try:
        with open(file_path, "wb") as buffer:
            buffer.write(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    # Lưu thông tin file vào database
    file_type = file_processor.get_file_type(file.filename)
    db_file = UploadedFile(
        filename=filename,
        original_filename=file.filename,
        file_path=file_path,
        file_type=file_type,
        file_size=file_size,
        processed="pending"
    )
    db.add(db_file)
    db.commit()
    db.refresh(db_file)
    
    # Xử lý file trong background
    background_tasks.add_task(process_file_background, db_file.id, file_path, file_type)
    
    return {
        "message": "File uploaded successfully",
        "file_id": db_file.id,
        "filename": file.filename,
        "file_type": file_type,
        "file_size": file_size,
        "status": "processing"
    }

@router.get("/files")
async def list_files(db: Session = Depends(get_db)):
    """Lấy danh sách file đã upload"""
    files = db.query(UploadedFile).all()
    return {
        "files": [
            {
                "id": f.id,
                "filename": f.original_filename,
                "file_type": f.file_type,
                "file_size": f.file_size,
                "upload_time": f.upload_time,
                "processed": f.processed
            }
            for f in files
        ]
    }

@router.get("/files/{file_id}")
async def get_file_info(file_id: int, db: Session = Depends(get_db)):
    """Lấy thông tin chi tiết của file"""
    file_record = db.query(UploadedFile).filter(UploadedFile.id == file_id).first()
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    
    return {
        "id": file_record.id,
        "filename": file_record.original_filename,
        "file_type": file_record.file_type,
        "file_size": file_record.file_size,
        "upload_time": file_record.upload_time,
        "processed": file_record.processed,
        "extracted_content": file_record.extracted_content
    }

@router.delete("/files/{file_id}")
async def delete_file(file_id: int, db: Session = Depends(get_db)):
    """Xóa file"""
    file_record = db.query(UploadedFile).filter(UploadedFile.id == file_id).first()
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    
    # Xóa file từ disk
    try:
        if os.path.exists(file_record.file_path):
            os.remove(file_record.file_path)
    except Exception as e:
        print(f"Warning: Could not delete file from disk: {e}")
    
    # Xóa record từ database
    db.delete(file_record)
    db.commit()
    
    return {"message": "File deleted successfully"}

async def process_file_background(file_id: int, file_path: str, file_type: str):
    """Xử lý file trong background"""
    from app.core.database import SessionLocal
    
    db = SessionLocal()
    try:
        # Cập nhật status thành processing
        file_record = db.query(UploadedFile).filter(UploadedFile.id == file_id).first()
        if file_record:
            file_record.processed = "processing"
            db.commit()
        
        # Xử lý file
        result = file_processor.process_file(file_path, file_type)
        
        # Cập nhật kết quả
        if file_record:
            if result['success']:
                file_record.processed = "completed"
                file_record.extracted_content = str(result['content'])
            else:
                file_record.processed = "failed"
                file_record.extracted_content = f"Error: {result['error']}"
            
            db.commit()
    
    except Exception as e:
        # Cập nhật status thành failed
        if file_record:
            file_record.processed = "failed"
            file_record.extracted_content = f"Processing error: {str(e)}"
            db.commit()
    
    finally:
        db.close()
