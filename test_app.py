"""
Tests cơ bản cho AI Data Analysis Platform
"""

import pytest
import os
import tempfile
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import io

from main import app
from app.core.database import get_db, Base
from app.services.file_processor import FileProcessor
from app.services.llama_service import LlamaService

# Tạo test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

# Tạo test client
client = TestClient(app)

@pytest.fixture(scope="module")
def setup_database():
    """Setup test database"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)
    if os.path.exists("test.db"):
        os.remove("test.db")

def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_home_page():
    """Test home page"""
    response = client.get("/")
    assert response.status_code == 200

def test_file_list_empty(setup_database):
    """Test file list when empty"""
    response = client.get("/api/v1/files")
    assert response.status_code == 200
    assert response.json()["files"] == []

def test_upload_invalid_file(setup_database):
    """Test upload invalid file"""
    # Tạo file không hợp lệ
    invalid_file = io.BytesIO(b"invalid content")
    
    response = client.post(
        "/api/v1/upload",
        files={"file": ("test.xyz", invalid_file, "application/octet-stream")}
    )
    assert response.status_code == 400

def test_upload_csv_file(setup_database):
    """Test upload CSV file"""
    # Tạo CSV test file
    csv_content = "name,age,city\nJohn,25,NYC\nJane,30,LA\n"
    csv_file = io.BytesIO(csv_content.encode())
    
    response = client.post(
        "/api/v1/upload",
        files={"file": ("test.csv", csv_file, "text/csv")}
    )
    assert response.status_code == 200
    assert "file_id" in response.json()
    assert response.json()["file_type"] == "excel"

def test_chat_without_file(setup_database):
    """Test chat without file"""
    response = client.post(
        "/api/v1/chat",
        json={
            "message": "Xin chào",
            "session_id": "test_session"
        }
    )
    assert response.status_code == 200
    assert "response" in response.json()

def test_chat_status():
    """Test AI status endpoint"""
    response = client.get("/api/v1/chat/status")
    assert response.status_code == 200
    assert "llama_initialized" in response.json()
    assert "fallback_mode" in response.json()

def test_file_processor():
    """Test FileProcessor class"""
    processor = FileProcessor()
    
    # Test get_file_type
    assert processor.get_file_type("test.csv") == "excel"
    assert processor.get_file_type("test.docx") == "word"
    assert processor.get_file_type("test.pdf") == "pdf"
    assert processor.get_file_type("test.jpg") == "image"
    assert processor.get_file_type("test.mp3") == "audio"
    assert processor.get_file_type("test.xyz") == "unknown"

def test_llama_service():
    """Test LlamaService class"""
    service = LlamaService()
    
    # Test fallback response
    response = service.generate_response("Xin chào")
    assert isinstance(response, str)
    assert len(response) > 0
    
    # Test with file content
    file_content = {
        "type": "excel",
        "summary": {
            "shape": [10, 3],
            "columns": ["name", "age", "city"]
        }
    }
    response = service.generate_response("Tóm tắt file", file_content=file_content)
    assert "Excel" in response

def test_create_temp_files():
    """Test tạo file tạm để test"""
    # Tạo temp directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test CSV
        csv_path = os.path.join(temp_dir, "test.csv")
        with open(csv_path, "w") as f:
            f.write("name,age\nJohn,25\nJane,30\n")
        
        processor = FileProcessor()
        result = processor.process_file(csv_path, "excel")
        assert result["success"] == True
        assert "summary" in result["content"]
        
        # Test text file
        txt_path = os.path.join(temp_dir, "test.txt")
        with open(txt_path, "w", encoding="utf-8") as f:
            f.write("Đây là nội dung test file.")
        
        result = processor.process_file(txt_path, "word")
        assert result["success"] == True
        assert "text" in result["content"]

if __name__ == "__main__":
    # Chạy tests
    pytest.main([__file__, "-v"])
