"""
Router cho phân tích dữ liệu
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import Dict, Any
import json

from app.core.database import get_db, UploadedFile
from app.services.file_processor import FileProcessor

router = APIRouter()
file_processor = FileProcessor()

@router.get("/analysis/{file_id}")
async def analyze_file(file_id: int, db: Session = Depends(get_db)):
    """Phân tích file đã upload"""
    
    # Lấy thông tin file
    file_record = db.query(UploadedFile).filter(UploadedFile.id == file_id).first()
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    
    if file_record.processed != "completed":
        raise HTTPException(
            status_code=400, 
            detail=f"File processing status: {file_record.processed}"
        )
    
    try:
        # Parse extracted content
        content = eval(file_record.extracted_content) if file_record.extracted_content else {}
        
        # Tạo analysis summary
        analysis = generate_analysis_summary(content, file_record.file_type)
        
        return {
            "file_id": file_id,
            "filename": file_record.original_filename,
            "file_type": file_record.file_type,
            "analysis": analysis,
            "extracted_content": content
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@router.get("/analysis/{file_id}/summary")
async def get_file_summary(file_id: int, db: Session = Depends(get_db)):
    """Lấy tóm tắt nhanh của file"""
    
    file_record = db.query(UploadedFile).filter(UploadedFile.id == file_id).first()
    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")
    
    if file_record.processed != "completed":
        return {
            "file_id": file_id,
            "status": file_record.processed,
            "summary": "File is still being processed"
        }
    
    try:
        content = eval(file_record.extracted_content) if file_record.extracted_content else {}
        summary = generate_quick_summary(content, file_record.file_type)
        
        return {
            "file_id": file_id,
            "filename": file_record.original_filename,
            "file_type": file_record.file_type,
            "status": file_record.processed,
            "summary": summary
        }
    
    except Exception as e:
        return {
            "file_id": file_id,
            "status": "error",
            "summary": f"Error generating summary: {str(e)}"
        }

def generate_analysis_summary(content: Dict[str, Any], file_type: str) -> Dict[str, Any]:
    """Tạo phân tích chi tiết cho file"""
    
    analysis = {
        "file_type": file_type,
        "processing_status": "completed"
    }
    
    if file_type == "excel":
        if "summary" in content:
            summary = content["summary"]
            analysis.update({
                "data_shape": summary.get("shape", [0, 0]),
                "columns": summary.get("columns", []),
                "data_types": summary.get("dtypes", {}),
                "missing_values": summary.get("null_counts", {}),
                "numeric_summary": summary.get("describe", {}),
                "insights": generate_excel_insights(summary)
            })
    
    elif file_type in ["word", "pdf"]:
        if "text" in content:
            text = content["text"]
            analysis.update({
                "word_count": content.get("word_count", 0),
                "character_count": content.get("char_count", 0),
                "text_preview": text[:500] + "..." if len(text) > 500 else text,
                "insights": generate_text_insights(content)
            })
    
    elif file_type == "image":
        if "text" in content:
            analysis.update({
                "extracted_text": content.get("text", ""),
                "image_size": content.get("image_size", [0, 0]),
                "image_mode": content.get("image_mode", ""),
                "word_count": content.get("word_count", 0),
                "insights": generate_image_insights(content)
            })
    
    elif file_type == "audio":
        if "text" in content:
            analysis.update({
                "transcribed_text": content.get("text", ""),
                "duration": content.get("duration", 0),
                "word_count": content.get("word_count", 0),
                "insights": generate_audio_insights(content)
            })
    
    return analysis

def generate_quick_summary(content: Dict[str, Any], file_type: str) -> str:
    """Tạo tóm tắt nhanh cho file"""
    
    if file_type == "excel":
        if "summary" in content:
            shape = content["summary"].get("shape", [0, 0])
            columns = len(content["summary"].get("columns", []))
            return f"Excel file with {shape[0]} rows and {columns} columns"
    
    elif file_type in ["word", "pdf"]:
        word_count = content.get("word_count", 0)
        return f"{file_type.upper()} file with {word_count} words"
    
    elif file_type == "image":
        size = content.get("image_size", [0, 0])
        word_count = content.get("word_count", 0)
        return f"Image {size[0]}x{size[1]} with {word_count} words extracted"
    
    elif file_type == "audio":
        duration = content.get("duration", 0)
        word_count = content.get("word_count", 0)
        return f"Audio file {duration:.1f}s with {word_count} words transcribed"
    
    return f"Processed {file_type} file"

def generate_excel_insights(summary: Dict[str, Any]) -> list:
    """Tạo insights cho Excel file"""
    insights = []
    
    shape = summary.get("shape", [0, 0])
    if shape[0] > 1000:
        insights.append("Large dataset with over 1000 rows")
    
    null_counts = summary.get("null_counts", {})
    missing_cols = [col for col, count in null_counts.items() if count > 0]
    if missing_cols:
        insights.append(f"Missing values found in columns: {', '.join(missing_cols[:3])}")
    
    dtypes = summary.get("dtypes", {})
    numeric_cols = [col for col, dtype in dtypes.items() if 'int' in str(dtype) or 'float' in str(dtype)]
    if numeric_cols:
        insights.append(f"Numeric columns available for analysis: {len(numeric_cols)}")
    
    return insights

def generate_text_insights(content: Dict[str, Any]) -> list:
    """Tạo insights cho text file"""
    insights = []
    
    word_count = content.get("word_count", 0)
    if word_count > 1000:
        insights.append("Long document suitable for detailed analysis")
    elif word_count < 100:
        insights.append("Short document")
    
    text = content.get("text", "")
    if any(keyword in text.lower() for keyword in ["data", "analysis", "report"]):
        insights.append("Document appears to contain analytical content")
    
    return insights

def generate_image_insights(content: Dict[str, Any]) -> list:
    """Tạo insights cho image file"""
    insights = []
    
    word_count = content.get("word_count", 0)
    if word_count > 0:
        insights.append(f"Text successfully extracted from image")
    else:
        insights.append("No text detected in image")
    
    size = content.get("image_size", [0, 0])
    if size[0] * size[1] > 1000000:  # > 1MP
        insights.append("High resolution image")
    
    return insights

def generate_audio_insights(content: Dict[str, Any]) -> list:
    """Tạo insights cho audio file"""
    insights = []
    
    duration = content.get("duration", 0)
    if duration > 300:  # > 5 minutes
        insights.append("Long audio recording")
    
    word_count = content.get("word_count", 0)
    if word_count > 0:
        wpm = word_count / (duration / 60) if duration > 0 else 0
        insights.append(f"Speech rate: approximately {wpm:.0f} words per minute")
    
    return insights
