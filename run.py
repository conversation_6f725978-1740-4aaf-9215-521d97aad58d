"""
Script để chạy AI Data Analysis Platform
"""

import uvicorn
import os
import sys
from pathlib import Path

def main():
    """Chạy ứng dụng"""
    
    print("🚀 Starting AI Data Analysis Platform...")
    print("=" * 50)
    
    # Kiểm tra môi trường
    print("📋 Checking environment...")
    
    # Kiểm tra Python version
    python_version = sys.version_info
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Kiểm tra các thư mục cần thiết
    required_dirs = ["uploads", "static", "templates"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 Created directory: {dir_name}")
        else:
            print(f"✅ Directory exists: {dir_name}")
    
    # Kiểm tra file .env
    if os.path.exists(".env"):
        print("✅ .env file found")
    else:
        print("⚠️ .env file not found - using default settings")
    
    # Import và kiểm tra dependencies
    try:
        import fastapi
        print(f"✅ FastAPI {fastapi.__version__}")
    except ImportError:
        print("❌ FastAPI not installed")
        return
    
    try:
        import pandas
        print(f"✅ Pandas {pandas.__version__}")
    except ImportError:
        print("⚠️ Pandas not installed - Excel processing may not work")
    
    try:
        import PIL
        print(f"✅ Pillow {PIL.__version__}")
    except ImportError:
        print("⚠️ Pillow not installed - Image processing may not work")
    
    # Kiểm tra Llama
    try:
        import llama_cpp
        print("✅ llama-cpp-python available")
    except ImportError:
        print("⚠️ llama-cpp-python not installed - using fallback mode")
    
    print("=" * 50)
    print("🌐 Starting web server...")
    print("📍 URL: http://127.0.0.1:8000")
    print("📖 API Docs: http://127.0.0.1:8000/docs")
    print("🛑 Press Ctrl+C to stop")
    print("=" * 50)
    
    # Chạy server
    try:
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
