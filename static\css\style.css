/* AI Data Analysis Platform Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
}

.user-message {
    background-color: #e3f2fd;
    margin-left: 20px;
    border-left: 4px solid #2196f3;
}

.ai-message {
    background-color: #f3e5f5;
    margin-right: 20px;
    border-left: 4px solid #9c27b0;
}

.file-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #fff;
    transition: all 0.3s ease;
}

.file-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.file-status {
    font-size: 0.8em;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
}

.status-pending {
    background-color: #ffc107;
}

.status-processing {
    background-color: #17a2b8;
}

.status-completed {
    background-color: #28a745;
}

.status-failed {
    background-color: #dc3545;
}

.btn-file-action {
    font-size: 0.8em;
    padding: 2px 8px;
    margin: 2px;
}

.upload-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.upload-zone:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.upload-zone.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.progress {
    height: 8px;
    border-radius: 4px;
}

.alert {
    border-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chat-messages {
        height: 300px;
    }
    
    .user-message, .ai-message {
        margin-left: 0;
        margin-right: 0;
    }
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
