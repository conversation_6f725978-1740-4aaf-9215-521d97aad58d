"""
Router cho chatbot với tích hợp Llama AI
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
import uuid

from app.core.database import get_db, ChatHistory, UploadedFile
from app.services.llama_service import LlamaService

router = APIRouter()

# Khởi tạo Llama service
llama_service = LlamaService()

class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    file_id: Optional[int] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    file_id: Optional[int] = None

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(
    request: ChatRequest,
    db: Session = Depends(get_db)
):
    """Chat với AI sử dụng Llama"""

    # Tạo session_id nếu chưa có
    session_id = request.session_id or f"session_{str(uuid.uuid4())[:8]}"

    # L<PERSON>y thông tin file nếu có
    file_content = None
    file_record = None

    if request.file_id:
        file_record = db.query(UploadedFile).filter(UploadedFile.id == request.file_id).first()
        if file_record and file_record.processed == "completed":
            try:
                # Parse extracted content
                file_content = eval(file_record.extracted_content) if file_record.extracted_content else None
            except Exception as e:
                print(f"Error parsing file content: {e}")
                file_content = None
        elif file_record and file_record.processed != "completed":
            response_text = f"File '{file_record.original_filename}' đang được xử lý (trạng thái: {file_record.processed}). Vui lòng đợi một chút."
        elif not file_record:
            response_text = "File không tồn tại hoặc đã bị xóa."

    # Generate AI response nếu không có lỗi file
    if 'response_text' not in locals():
        try:
            response_text = llama_service.generate_response(
                user_message=request.message,
                context=None,
                file_content=file_content
            )
        except Exception as e:
            response_text = f"Xin lỗi, có lỗi xảy ra khi xử lý câu hỏi của bạn: {str(e)}"

    # Lưu lịch sử chat
    chat_record = ChatHistory(
        session_id=session_id,
        user_message=request.message,
        ai_response=response_text,
        file_id=request.file_id
    )
    db.add(chat_record)
    db.commit()

    return ChatResponse(
        response=response_text,
        session_id=session_id,
        file_id=request.file_id
    )

@router.get("/chat/history/{session_id}")
async def get_chat_history(session_id: str, db: Session = Depends(get_db)):
    """Lấy lịch sử chat"""

    history = db.query(ChatHistory).filter(
        ChatHistory.session_id == session_id
    ).order_by(ChatHistory.timestamp).all()

    return {
        "session_id": session_id,
        "history": [
            {
                "id": chat.id,
                "user_message": chat.user_message,
                "ai_response": chat.ai_response,
                "timestamp": chat.timestamp,
                "file_id": chat.file_id
            }
            for chat in history
        ]
    }

@router.get("/chat/status")
async def get_ai_status():
    """Kiểm tra trạng thái AI"""
    return {
        "llama_initialized": llama_service.is_initialized,
        "fallback_mode": llama_service.fallback_mode,
        "model_path": llama_service.model is not None,
        "status": "ready" if llama_service.is_initialized else "fallback"
    }
