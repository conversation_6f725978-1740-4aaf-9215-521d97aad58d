# Hướng dẫn cài đặt AI Data Analysis Platform

## Y<PERSON>u cầu hệ thống

- Python 3.8 trở lên
- Windows/Linux/macOS
- RAM: tối thiểu 4GB (khuyến nghị 8GB+)
- Dung lượng: 2GB trống

## Cài đặt nhanh

### 1. Clone hoặc tải project
```bash
# Nếu có git
git clone <repository-url>
cd ai-data-analysis

# Hoặc giải nén file zip vào thư mục
```

### 2. Tạo môi trường ảo (khuyến nghị)
```bash
# Tạo virtual environment
python -m venv venv

# Kích hoạt (Windows)
venv\Scripts\activate

# Kích hoạt (Linux/macOS)
source venv/bin/activate
```

### 3. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 4. Chạy ứng dụng
```bash
python run.py
```

### 5. T<PERSON>y cập ứng dụng
- Mở trình duyệt: http://127.0.0.1:8000
- API Documentation: http://127.0.0.1:8000/docs

## Cài đặt chi tiết

### Dependencies bổ sung cho các tính năng

#### 1. OCR (đọc text từ ảnh)
```bash
# Cài đặt Tesseract OCR
# Windows: Tải từ https://github.com/UB-Mannheim/tesseract/wiki
# Ubuntu: sudo apt install tesseract-ocr
# macOS: brew install tesseract

pip install pytesseract
```

#### 2. Speech Recognition (chuyển đổi audio)
```bash
pip install SpeechRecognition
# Có thể cần cài thêm: pip install pyaudio
```

#### 3. Llama AI Model (tùy chọn)
```bash
# Cài đặt llama-cpp-python
pip install llama-cpp-python

# Tải model Llama (ví dụ)
# Đặt đường dẫn model trong file .env
```

### Cấu hình môi trường

#### File .env
```env
# Server
HOST=127.0.0.1
PORT=8000
DEBUG=True

# Database
DATABASE_URL=sqlite:///./ai_data_analysis.db

# File Upload
MAX_FILE_SIZE=52428800
UPLOAD_DIR=uploads

# AI/Llama (tùy chọn)
LLAMA_MODEL_PATH=/path/to/your/llama/model.bin
LLAMA_MODEL_NAME=llama-2-7b-chat
MAX_TOKENS=2048
TEMPERATURE=0.7
```

## Kiểm tra cài đặt

### Chạy tests
```bash
python -m pytest test_app.py -v
```

### Kiểm tra các tính năng
1. **Upload file**: Thử upload file CSV, Excel, PDF
2. **Chat**: Đặt câu hỏi với AI
3. **Phân tích**: Xem kết quả phân tích file

## Xử lý lỗi thường gặp

### 1. Lỗi import pandas
```bash
pip install pandas openpyxl
```

### 2. Lỗi OCR
```bash
# Cài đặt Tesseract OCR trước
pip install pytesseract
```

### 3. Lỗi audio processing
```bash
pip install pydub SpeechRecognition
# Windows có thể cần: pip install pyaudio
```

### 4. Lỗi port đã được sử dụng
- Thay đổi PORT trong .env
- Hoặc dừng process đang dùng port 8000

### 5. Lỗi permission
```bash
# Linux/macOS
chmod +x run.py
```

## Cấu trúc thư mục sau cài đặt

```
ai-data-analysis/
├── app/                 # Source code
├── static/             # CSS, JS files
├── templates/          # HTML templates
├── uploads/            # Uploaded files
├── main.py            # Entry point
├── run.py             # Run script
├── requirements.txt   # Dependencies
├── .env              # Environment config
└── README.md         # Documentation
```

## Nâng cấp

### Cập nhật dependencies
```bash
pip install -r requirements.txt --upgrade
```

### Backup dữ liệu
```bash
# Backup database
cp ai_data_analysis.db ai_data_analysis.db.backup

# Backup uploads
cp -r uploads uploads_backup
```

## Hỗ trợ

- Kiểm tra logs trong terminal
- Xem API docs tại /docs
- Kiểm tra file .env
- Đảm bảo Python 3.8+

## Production Deployment

### Sử dụng Gunicorn (Linux/macOS)
```bash
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Sử dụng Docker
```dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "run.py"]
```

### Nginx Reverse Proxy
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```
